AWSTemplateFormatVersion: '2010-09-09'
Description: 'SupplyLine MRO Suite - Application Deployment'

Parameters:
  InfrastructureStackName:
    Type: String
    Description: Name of the infrastructure CloudFormation stack
  
  Environment:
    Type: String
    Default: production
    AllowedValues: [development, staging, production]
    Description: Environment name
  
  BackendImageUri:
    Type: String
    Description: ECR URI for backend Docker image
  
  DomainName:
    Type: String
    Default: ''
    Description: Custom domain name (optional)

Resources:
  # ECS Cluster
  ECSCluster:
    Type: AWS::ECS::Cluster
    Properties:
      ClusterName: !Sub '${AWS::StackName}-cluster'
      CapacityProviders:
        - FARGATE
        - FARGATE_SPOT
      DefaultCapacityProviderStrategy:
        - CapacityProvider: FARGATE
          Weight: 1
        - CapacityProvider: FARGATE_SPOT
          Weight: 4

  # ECS Task Definition
  BackendTaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: !Sub '${AWS::StackName}-backend'
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      Cpu: 512
      Memory: 1024
      ExecutionRoleArn: !Ref ECSExecutionRole
      TaskRoleArn: !Ref ECSTaskRole
      ContainerDefinitions:
        - Name: backend
          Image: !Ref BackendImageUri
          Essential: true
          PortMappings:
            - ContainerPort: 5000
              Protocol: tcp
          Environment:
            - Name: FLASK_ENV
              Value: !Ref Environment
            - Name: DATABASE_URL
              Value: !Sub 
                - 'postgresql://supplyline_admin:${Password}@${Endpoint}:${Port}/postgres'
                - Endpoint: 
                    Fn::ImportValue: !Sub '${InfrastructureStackName}-DatabaseEndpoint'
                  Port:
                    Fn::ImportValue: !Sub '${InfrastructureStackName}-DatabasePort'
                  Password: !Ref DatabasePassword
            - Name: JWT_SECRET_KEY
              Value: !Ref JWTSecretKey
            - Name: SECRET_KEY
              Value: !Ref AppSecretKey
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref BackendLogGroup
              awslogs-region: !Ref AWS::Region
              awslogs-stream-prefix: backend
          HealthCheck:
            Command:
              - CMD-SHELL
              - curl -f http://localhost:5000/health || exit 1
            Interval: 30
            Timeout: 5
            Retries: 3
            StartPeriod: 60

  # Secrets Manager for sensitive data
  DatabasePassword:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub '${AWS::StackName}/database/password'
      Description: Database password for SupplyLine MRO Suite
      GenerateSecretString:
        SecretStringTemplate: '{"username": "supplyline_admin"}'
        GenerateStringKey: 'password'
        PasswordLength: 32
        ExcludeCharacters: '"@/\'

  JWTSecretKey:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub '${AWS::StackName}/jwt/secret'
      Description: JWT secret key for SupplyLine MRO Suite
      GenerateSecretString:
        PasswordLength: 64
        ExcludeCharacters: '"@/\'

  AppSecretKey:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub '${AWS::StackName}/app/secret'
      Description: Application secret key for SupplyLine MRO Suite
      GenerateSecretString:
        PasswordLength: 64
        ExcludeCharacters: '"@/\'

  # IAM Roles
  ECSExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy
      Policies:
        - PolicyName: SecretsManagerAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                Resource:
                  - !Ref DatabasePassword
                  - !Ref JWTSecretKey
                  - !Ref AppSecretKey

  ECSTaskRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: S3Access
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                  - s3:DeleteObject
                Resource:
                  - !Sub 
                    - '${BucketArn}/*'
                    - BucketArn:
                        Fn::ImportValue: !Sub '${InfrastructureStackName}-FrontendBucket'

  # CloudWatch Log Group
  BackendLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/ecs/${AWS::StackName}-backend'
      RetentionInDays: 30

  # Application Load Balancer
  ApplicationLoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Name: !Sub '${AWS::StackName}-alb'
      Scheme: internet-facing
      Type: application
      Subnets:
        - Fn::ImportValue: !Sub '${InfrastructureStackName}-PublicSubnet1Id'
        - Fn::ImportValue: !Sub '${InfrastructureStackName}-PublicSubnet2Id'
      SecurityGroups:
        - Fn::ImportValue: !Sub '${InfrastructureStackName}-ApplicationSecurityGroup'

  # ALB Target Group
  BackendTargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Name: !Sub '${AWS::StackName}-backend-tg'
      Port: 5000
      Protocol: HTTP
      VpcId:
        Fn::ImportValue: !Sub '${InfrastructureStackName}-VpcId'
      TargetType: ip
      HealthCheckPath: /health
      HealthCheckProtocol: HTTP
      HealthCheckIntervalSeconds: 30
      HealthCheckTimeoutSeconds: 5
      HealthyThresholdCount: 2
      UnhealthyThresholdCount: 3

  # ALB Listener
  BackendListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      DefaultActions:
        - Type: forward
          TargetGroupArn: !Ref BackendTargetGroup
      LoadBalancerArn: !Ref ApplicationLoadBalancer
      Port: 80
      Protocol: HTTP

  # ECS Service
  BackendService:
    Type: AWS::ECS::Service
    DependsOn: BackendListener
    Properties:
      ServiceName: !Sub '${AWS::StackName}-backend-service'
      Cluster: !Ref ECSCluster
      TaskDefinition: !Ref BackendTaskDefinition
      DesiredCount: 2
      LaunchType: FARGATE
      NetworkConfiguration:
        AwsvpcConfiguration:
          SecurityGroups:
            - Fn::ImportValue: !Sub '${InfrastructureStackName}-ApplicationSecurityGroup'
          Subnets:
            - Fn::ImportValue: !Sub '${InfrastructureStackName}-PublicSubnet1Id'
            - Fn::ImportValue: !Sub '${InfrastructureStackName}-PublicSubnet2Id'
          AssignPublicIp: ENABLED
      LoadBalancers:
        - ContainerName: backend
          ContainerPort: 5000
          TargetGroupArn: !Ref BackendTargetGroup
      DeploymentConfiguration:
        MaximumPercent: 200
        MinimumHealthyPercent: 50
        DeploymentCircuitBreaker:
          Enable: true
          Rollback: true

  # CloudFront Distribution
  CloudFrontDistribution:
    Type: AWS::CloudFront::Distribution
    Properties:
      DistributionConfig:
        Origins:
          - Id: S3Origin
            DomainName: !Sub 
              - '${Bucket}.s3.${AWS::Region}.amazonaws.com'
              - Bucket:
                  Fn::ImportValue: !Sub '${InfrastructureStackName}-FrontendBucket'
            S3OriginConfig:
              OriginAccessIdentity: !Sub 
                - 'origin-access-identity/cloudfront/${OAI}'
                - OAI:
                    Fn::ImportValue: !Sub '${InfrastructureStackName}-CloudFrontOAI'
          - Id: ALBOrigin
            DomainName: !GetAtt ApplicationLoadBalancer.DNSName
            CustomOriginConfig:
              HTTPPort: 80
              HTTPSPort: 443
              OriginProtocolPolicy: http-only
        Enabled: true
        DefaultRootObject: index.html
        DefaultCacheBehavior:
          TargetOriginId: S3Origin
          ViewerProtocolPolicy: redirect-to-https
          AllowedMethods:
            - GET
            - HEAD
            - OPTIONS
          CachedMethods:
            - GET
            - HEAD
          ForwardedValues:
            QueryString: false
            Cookies:
              Forward: none
        CacheBehaviors:
          - PathPattern: '/api/*'
            TargetOriginId: ALBOrigin
            ViewerProtocolPolicy: redirect-to-https
            AllowedMethods:
              - DELETE
              - GET
              - HEAD
              - OPTIONS
              - PATCH
              - POST
              - PUT
            CachedMethods:
              - GET
              - HEAD
            ForwardedValues:
              QueryString: true
              Headers:
                - Authorization
                - Content-Type
              Cookies:
                Forward: none
            TTL:
              DefaultTTL: 0
              MaxTTL: 0
              MinTTL: 0
        PriceClass: PriceClass_100
        ViewerCertificate:
          CloudFrontDefaultCertificate: true

Outputs:
  LoadBalancerDNS:
    Description: Application Load Balancer DNS name
    Value: !GetAtt ApplicationLoadBalancer.DNSName
    Export:
      Name: !Sub '${AWS::StackName}-LoadBalancerDNS'

  CloudFrontDomainName:
    Description: CloudFront distribution domain name
    Value: !GetAtt CloudFrontDistribution.DomainName
    Export:
      Name: !Sub '${AWS::StackName}-CloudFrontDomain'

  BackendServiceArn:
    Description: ECS Backend Service ARN
    Value: !Ref BackendService
    Export:
      Name: !Sub '${AWS::StackName}-BackendServiceArn'
