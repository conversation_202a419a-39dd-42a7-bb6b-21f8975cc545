{"tests/test_auth_security.py::TestJWTSecurity::test_jwt_token_validation": true, "tests/test_auth_security.py::TestJWTSecurity::test_jwt_token_expiration": true, "tests/test_auth_security.py::TestJWTSecurity::test_jwt_token_tampering": true, "tests/test_auth_security.py::TestJWTSecurity::test_jwt_algorithm_confusion": true, "tests/test_auth_security.py::TestSessionSecurity::test_concurrent_login_handling": true, "tests/test_auth_security.py::TestSessionSecurity::test_logout_token_invalidation": true, "tests/test_auth.py::TestJWTAuthentication::test_login_success": true, "tests/test_auth.py::TestJWTAuthentication::test_login_invalid_credentials": true, "tests/test_auth.py::TestJWTAuthentication::test_login_missing_credentials": true, "tests/test_auth.py::TestJWTAuthentication::test_token_refresh_success": true, "tests/test_auth.py::TestJWTAuthentication::test_logout_success": true, "tests/test_auth.py::TestJWTAuthentication::test_get_current_user_success": true, "tests/test_auth.py::TestJWTAuthentication::test_auth_status_authenticated": true, "tests/test_auth.py::TestJWTManager::test_generate_tokens": true, "tests/test_auth.py::TestJWTManager::test_verify_access_token": true, "tests/test_auth.py::TestJWTManager::test_verify_refresh_token": true, "tests/test_auth.py::TestJWTManager::test_refresh_access_token": true, "tests/test_authorization.py::TestRoleBasedAccessControl::test_admin_only_endpoints": true, "tests/test_authorization.py::TestRoleBasedAccessControl::test_user_data_isolation": true, "tests/test_authorization.py::TestRoleBasedAccessControl::test_inactive_user_access": true, "tests/test_authorization.py::TestPrivilegeEscalation::test_user_cannot_promote_self": true, "tests/test_authorization.py::TestPrivilegeEscalation::test_user_cannot_modify_other_users": true, "tests/test_authorization.py::TestResourceAccess::test_tool_access_control": true, "tests/test_authorization.py::TestResourceAccess::test_checkout_authorization": true, "tests/test_routes.py::TestToolRoutes::test_get_tools_authenticated": true, "tests/test_routes.py::TestToolRoutes::test_get_tools_unauthenticated": true, "tests/test_security_assessment.py::TestSecurityAssessment::test_authentication_security": true, "tests/test_security_assessment.py::TestSecurityAssessment::test_cors_and_headers": true, "tests/test_security_assessment.py::TestSecurityAssessment::test_error_handling_security": true, "tests/test_security_assessment.py::TestSecurityAssessment::test_generate_security_report": true}