["tests/test_auth.py::TestJWTAuthentication::test_auth_status_authenticated", "tests/test_auth.py::TestJWTAuthentication::test_auth_status_unauthenticated", "tests/test_auth.py::TestJWTAuthentication::test_get_current_user_success", "tests/test_auth.py::TestJWTAuthentication::test_get_current_user_without_token", "tests/test_auth.py::TestJWTAuthentication::test_login_inactive_user", "tests/test_auth.py::TestJWTAuthentication::test_login_invalid_credentials", "tests/test_auth.py::TestJWTAuthentication::test_login_missing_credentials", "tests/test_auth.py::TestJWTAuthentication::test_login_nonexistent_user", "tests/test_auth.py::TestJWTAuthentication::test_login_success", "tests/test_auth.py::TestJWTAuthentication::test_logout_success", "tests/test_auth.py::TestJWTAuthentication::test_logout_without_token", "tests/test_auth.py::TestJWTAuthentication::test_token_refresh_invalid_token", "tests/test_auth.py::TestJWTAuthentication::test_token_refresh_success", "tests/test_auth.py::TestJWTManager::test_generate_tokens", "tests/test_auth.py::TestJWTManager::test_refresh_access_token", "tests/test_auth.py::TestJWTManager::test_verify_access_token", "tests/test_auth.py::TestJWTManager::test_verify_invalid_token", "tests/test_auth.py::TestJWTManager::test_verify_refresh_token", "tests/test_auth_security.py::TestJWTSecurity::test_jwt_algorithm_confusion", "tests/test_auth_security.py::TestJWTSecurity::test_jwt_token_expiration", "tests/test_auth_security.py::TestJWTSecurity::test_jwt_token_tampering", "tests/test_auth_security.py::TestJWTSecurity::test_jwt_token_validation", "tests/test_auth_security.py::TestPasswordSecurity::test_password_confirmation_mismatch", "tests/test_auth_security.py::TestPasswordSecurity::test_password_hashing", "tests/test_auth_security.py::TestPasswordSecurity::test_weak_password_rejection", "tests/test_auth_security.py::TestSessionSecurity::test_concurrent_login_handling", "tests/test_auth_security.py::TestSessionSecurity::test_logout_token_invalidation", "tests/test_authorization.py::TestPrivilegeEscalation::test_user_cannot_modify_other_users", "tests/test_authorization.py::TestPrivilegeEscalation::test_user_cannot_promote_self", "tests/test_authorization.py::TestResourceAccess::test_checkout_authorization", "tests/test_authorization.py::TestResourceAccess::test_tool_access_control", "tests/test_authorization.py::TestRoleBasedAccessControl::test_admin_only_endpoints", "tests/test_authorization.py::TestRoleBasedAccessControl::test_inactive_user_access", "tests/test_authorization.py::TestRoleBasedAccessControl::test_user_data_isolation", "tests/test_input_validation.py::TestSQLInjectionPrevention::test_login_sql_injection", "tests/test_models.py::TestCheckoutModel::test_create_checkout", "tests/test_models.py::TestChemicalModel::test_chemical_expiration", "tests/test_models.py::TestChemicalModel::test_chemical_low_stock", "tests/test_models.py::TestChemicalModel::test_create_chemical", "tests/test_models.py::TestRolePermissionModel::test_create_role_with_permissions", "tests/test_models.py::TestToolModel::test_create_tool", "tests/test_models.py::TestToolModel::test_tool_calibration_status", "tests/test_models.py::TestToolModel::test_tool_to_dict", "tests/test_models.py::TestUserModel::test_create_user", "tests/test_models.py::TestUserModel::test_user_account_lockout", "tests/test_models.py::TestUserModel::test_user_password_hashing", "tests/test_models.py::TestUserModel::test_user_remember_token", "tests/test_models.py::TestUserModel::test_user_reset_token", "tests/test_models.py::TestUserModel::test_user_to_dict", "tests/test_routes.py::TestToolRoutes::test_get_tools_authenticated", "tests/test_routes.py::TestToolRoutes::test_get_tools_unauthenticated", "tests/test_security_assessment.py::TestSecurityAssessment::test_authentication_security", "tests/test_security_assessment.py::TestSecurityAssessment::test_cors_and_headers", "tests/test_security_assessment.py::TestSecurityAssessment::test_error_handling_security", "tests/test_security_assessment.py::TestSecurityAssessment::test_generate_security_report", "tests/test_security_assessment.py::TestSecurityAssessment::test_input_validation", "tests/test_security_assessment.py::TestSecurityAssessment::test_password_security", "tests/test_security_assessment.py::TestSecurityAssessment::test_rate_limiting_protection", "tests/test_security_assessment.py::TestSecurityAssessment::test_sql_injection_protection"]